{"name": "npm-bundled", "version": "4.0.0", "description": "list things in node_modules that are bundledDependencies, or transitive dependencies thereof", "main": "lib/index.js", "repository": {"type": "git", "url": "git+https://github.com/npm/npm-bundled.git"}, "author": "GitHub Inc.", "license": "ISC", "devDependencies": {"@npmcli/eslint-config": "^5.0.0", "@npmcli/template-oss": "4.23.3", "mutate-fs": "^2.1.1", "tap": "^16.3.0"}, "scripts": {"test": "tap", "lint": "npm run eslint", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run eslint -- --fix", "snap": "tap", "posttest": "npm run lint", "eslint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\""}, "files": ["bin/", "lib/"], "dependencies": {"npm-normalize-package-bin": "^4.0.0"}, "engines": {"node": "^18.17.0 || >=20.5.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.23.3", "publish": true}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}