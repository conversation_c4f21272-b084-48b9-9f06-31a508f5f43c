{"name": "is-network-error", "version": "1.1.0", "description": "Check if a value is a Fetch network error", "license": "MIT", "repository": "sindresorhus/is-network-error", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=16"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["network", "error", "fetch", "whatwg", "detect", "check", "typeerror"], "devDependencies": {"ava": "^5.3.1", "tsd": "^0.29.0", "xo": "^0.56.0"}}