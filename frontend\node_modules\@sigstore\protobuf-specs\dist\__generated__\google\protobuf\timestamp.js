"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.30.2
// source: google/protobuf/timestamp.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.Timestamp = void 0;
exports.Timestamp = {
    fromJSON(object) {
        return {
            seconds: isSet(object.seconds) ? globalThis.String(object.seconds) : "0",
            nanos: isSet(object.nanos) ? globalThis.Number(object.nanos) : 0,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.seconds !== "0") {
            obj.seconds = message.seconds;
        }
        if (message.nanos !== 0) {
            obj.nanos = Math.round(message.nanos);
        }
        return obj;
    },
};
function isSet(value) {
    return value !== null && value !== undefined;
}
