{"name": "ecommerce-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ecommerce", "api", "nodejs", "express"], "author": "", "license": "ISC", "description": "Backend API for ecommerce website", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "multer": "^2.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}