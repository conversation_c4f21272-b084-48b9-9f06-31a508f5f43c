{"name": "p-map", "version": "7.0.3", "description": "Map over promises concurrently", "license": "MIT", "repository": "sindresorhus/p-map", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "map", "resolved", "wait", "collection", "iterable", "iterator", "race", "fulfilled", "async", "await", "promises", "concurrently", "concurrency", "parallel", "bluebird"], "devDependencies": {"ava": "^5.2.0", "chalk": "^5.3.0", "delay": "^6.0.0", "in-range": "^3.0.0", "random-int": "^3.0.0", "time-span": "^5.1.0", "tsd": "^0.29.0", "xo": "^0.56.0"}}