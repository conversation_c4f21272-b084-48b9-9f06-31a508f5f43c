{"version": 3, "file": "animations.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-browser/animations/src/providers.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-browser/animations/src/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AnimationDriver,\n  NoopAnimationDriver,\n  ɵAnimationEngine as AnimationEngine,\n  ɵAnimationRendererFactory as AnimationRendererFactory,\n  ɵAnimationStyleNormalizer as AnimationStyleNormalizer,\n  ɵWebAnimationsDriver as WebAnimationsDriver,\n  ɵWebAnimationsStyleNormalizer as WebAnimationsStyleNormalizer,\n} from '@angular/animations/browser';\nimport {DOCUMENT} from '@angular/common';\nimport {\n  ANIMATION_MODULE_TYPE,\n  inject,\n  Inject,\n  Injectable,\n  NgZone,\n  OnDestroy,\n  Provider,\n  RendererFactory2,\n  ɵChangeDetectionScheduler as ChangeDetectionScheduler,\n} from '@angular/core';\nimport {ɵDomRendererFactory2 as DomRendererFactory2} from '../../index';\n\n@Injectable()\nexport class InjectableAnimationEngine extends AnimationEngine implements OnDestroy {\n  // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n  // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n  // both have `ngOnD<PERSON>roy` hooks and `flush()` must be called after all views are destroyed.\n  constructor(\n    @Inject(DOCUMENT) doc: Document,\n    driver: AnimationDriver,\n    normalizer: AnimationStyleNormalizer,\n  ) {\n    super(doc, driver, normalizer);\n  }\n\n  ngOnDestroy(): void {\n    this.flush();\n  }\n}\n\nexport function instantiateDefaultStyleNormalizer() {\n  return new WebAnimationsStyleNormalizer();\n}\n\nexport function instantiateRendererFactory(\n  renderer: DomRendererFactory2,\n  engine: AnimationEngine,\n  zone: NgZone,\n) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\n\nconst SHARED_ANIMATION_PROVIDERS: Provider[] = [\n  {provide: AnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer},\n  {provide: AnimationEngine, useClass: InjectableAnimationEngine},\n  {\n    provide: RendererFactory2,\n    useFactory: instantiateRendererFactory,\n    deps: [DomRendererFactory2, AnimationEngine, NgZone],\n  },\n];\n\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nexport const BROWSER_NOOP_ANIMATIONS_PROVIDERS: Provider[] = [\n  {provide: AnimationDriver, useClass: NoopAnimationDriver},\n  {provide: ANIMATION_MODULE_TYPE, useValue: 'NoopAnimations'},\n  ...SHARED_ANIMATION_PROVIDERS,\n];\n\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nexport const BROWSER_ANIMATIONS_PROVIDERS: Provider[] = [\n  // Note: the `ngServerMode` happen inside factories to give the variable time to initialize.\n  {\n    provide: AnimationDriver,\n    useFactory: () =>\n      typeof ngServerMode !== 'undefined' && ngServerMode\n        ? new NoopAnimationDriver()\n        : new WebAnimationsDriver(),\n  },\n  {\n    provide: ANIMATION_MODULE_TYPE,\n    useFactory: () =>\n      typeof ngServerMode !== 'undefined' && ngServerMode ? 'NoopAnimations' : 'BrowserAnimations',\n  },\n  ...SHARED_ANIMATION_PROVIDERS,\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {\n  ModuleWithProviders,\n  NgModule,\n  Provider,\n  ɵperformanceMarkFeature as performanceMarkFeature,\n} from '@angular/core';\nimport {BrowserModule} from '../../index';\n\nimport {BROWSER_ANIMATIONS_PROVIDERS, BROWSER_NOOP_ANIMATIONS_PROVIDERS} from './providers';\n\n/**\n * Object used to configure the behavior of {@link BrowserAnimationsModule}\n * @publicApi\n */\nexport interface BrowserAnimationsModuleConfig {\n  /**\n   *  Whether animations should be disabled. Passing this is identical to providing the\n   * `NoopAnimationsModule`, but it can be controlled based on a runtime value.\n   */\n  disableAnimations?: boolean;\n}\n\n/**\n * Exports `BrowserModule` with additional dependency-injection providers\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserModule],\n  providers: BROWSER_ANIMATIONS_PROVIDERS,\n})\nexport class BrowserAnimationsModule {\n  /**\n   * Configures the module based on the specified object.\n   *\n   * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n   * @see {@link BrowserAnimationsModuleConfig}\n   *\n   * @usageNotes\n   * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n   * function as follows:\n   * ```ts\n   * @NgModule({\n   *   imports: [BrowserAnimationsModule.withConfig(config)]\n   * })\n   * class MyNgModule {}\n   * ```\n   */\n  static withConfig(\n    config: BrowserAnimationsModuleConfig,\n  ): ModuleWithProviders<BrowserAnimationsModule> {\n    return {\n      ngModule: BrowserAnimationsModule,\n      providers: config.disableAnimations\n        ? BROWSER_NOOP_ANIMATIONS_PROVIDERS\n        : BROWSER_ANIMATIONS_PROVIDERS,\n    };\n  }\n}\n\n/**\n * Returns the set of dependency-injection providers\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nexport function provideAnimations(): Provider[] {\n  performanceMarkFeature('NgEagerAnimations');\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideAnimations` call results in app code.\n  return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserModule],\n  providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n})\nexport class NoopAnimationsModule {}\n\n/**\n * Returns the set of dependency-injection providers\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```ts\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nexport function provideNoopAnimations(): Provider[] {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideNoopAnimations` call results in app code.\n  return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n"], "names": ["AnimationEngine", "WebAnimationsStyleNormalizer", "AnimationRendererFactory", "AnimationStyleNormalizer", "WebAnimationsDriver", "performanceMarkFeature"], "mappings": ";;;;;;;;;;;;;;;AAgCM,MAAO,yBAA0B,SAAQA,gBAAe,CAAA;;;;AAI5D,IAAA,WAAA,CACoB,GAAa,EAC/B,MAAuB,EACvB,UAAoC,EAAA;AAEpC,QAAA,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,CAAC;;IAGhC,WAAW,GAAA;QACT,IAAI,CAAC,KAAK,EAAE;;AAbH,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,yBAAyB,kBAK1B,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,yBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHALP,yBAAyB,EAAA,CAAA;;sGAAzB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBADrC;;0BAMI,MAAM;2BAAC,QAAQ;;SAYJ,iCAAiC,GAAA;IAC/C,OAAO,IAAIC,6BAA4B,EAAE;AAC3C;SAEgB,0BAA0B,CACxC,QAA6B,EAC7B,MAAuB,EACvB,IAAY,EAAA;IAEZ,OAAO,IAAIC,yBAAwB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC;AAC7D;AAEA,MAAM,0BAA0B,GAAe;AAC7C,IAAA,EAAC,OAAO,EAAEC,yBAAwB,EAAE,UAAU,EAAE,iCAAiC,EAAC;AAClF,IAAA,EAAC,OAAO,EAAEH,gBAAe,EAAE,QAAQ,EAAE,yBAAyB,EAAC;AAC/D,IAAA;AACE,QAAA,OAAO,EAAE,gBAAgB;AACzB,QAAA,UAAU,EAAE,0BAA0B;AACtC,QAAA,IAAI,EAAE,CAAC,mBAAmB,EAAEA,gBAAe,EAAE,MAAM,CAAC;AACrD,KAAA;CACF;AAED;;;AAGG;AACI,MAAM,iCAAiC,GAAe;AAC3D,IAAA,EAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,mBAAmB,EAAC;AACzD,IAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,gBAAgB,EAAC;AAC5D,IAAA,GAAG,0BAA0B;CAC9B;AAED;;;AAGG;AACI,MAAM,4BAA4B,GAAe;;AAEtD,IAAA;AACE,QAAA,OAAO,EAAE,eAAe;QACxB,UAAU,EAAE,MACV,OAAO,YAAY,KAAK,WAAW,IAAI;cACnC,IAAI,mBAAmB;cACvB,IAAII,oBAAmB,EAAE;AAChC,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAE,qBAAqB;AAC9B,QAAA,UAAU,EAAE,MACV,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY,GAAG,gBAAgB,GAAG,mBAAmB;AAC/F,KAAA;AACD,IAAA,GAAG,0BAA0B;CAC9B;;ACvED;;;;AAIG;MAKU,uBAAuB,CAAA;AAClC;;;;;;;;;;;;;;;AAeG;IACH,OAAO,UAAU,CACf,MAAqC,EAAA;QAErC,OAAO;AACL,YAAA,QAAQ,EAAE,uBAAuB;YACjC,SAAS,EAAE,MAAM,CAAC;AAChB,kBAAE;AACF,kBAAE,4BAA4B;SACjC;;kHAzBQ,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,YAHxB,aAAa,CAAA,EAAA,CAAA;mHAGZ,uBAAuB,EAAA,SAAA,EAFvB,4BAA4B,EAAA,OAAA,EAAA,CAD7B,aAAa,CAAA,EAAA,CAAA;;sGAGZ,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAJnC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,CAAC;AACxB,oBAAA,SAAS,EAAE,4BAA4B;AACxC,iBAAA;;AA8BD;;;;;;;;;;;;;;;;;;;;;AAqBG;SACa,iBAAiB,GAAA;IAC/BC,uBAAsB,CAAC,mBAAmB,CAAC;;;AAG3C,IAAA,OAAO,CAAC,GAAG,4BAA4B,CAAC;AAC1C;AAEA;;;AAGG;MAKU,oBAAoB,CAAA;kHAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,YAHrB,aAAa,CAAA,EAAA,CAAA;mHAGZ,oBAAoB,EAAA,SAAA,EAFpB,iCAAiC,EAAA,OAAA,EAAA,CADlC,aAAa,CAAA,EAAA,CAAA;;sGAGZ,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,CAAC;AACxB,oBAAA,SAAS,EAAE,iCAAiC;AAC7C,iBAAA;;AAGD;;;;;;;;;;;;;;;;;;;;AAoBG;SACa,qBAAqB,GAAA;;;AAGnC,IAAA,OAAO,CAAC,GAAG,iCAAiC,CAAC;AAC/C;;;;"}