const express = require('express');
const Favorite = require('../models/Favorite');
const Product = require('../models/Product');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/favorites
// @desc    Get user's favorite products
// @access  Private
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 12 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const favorites = await Favorite.find({ user: req.user._id })
      .populate({
        path: 'product',
        match: { isActive: true },
        select: 'name price originalPrice images category brand rating stock discount'
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Filter out favorites where product is null (inactive products)
    const activeFavorites = favorites.filter(fav => fav.product !== null);

    const total = await Favorite.countDocuments({ 
      user: req.user._id,
      product: { $in: await Product.find({ isActive: true }).distinct('_id') }
    });
    
    const totalPages = Math.ceil(total / parseInt(limit));

    res.json({
      success: true,
      data: {
        favorites: activeFavorites,
        pagination: {
          currentPage: parseInt(page),
          totalPages,
          totalFavorites: total,
          hasNextPage: parseInt(page) < totalPages,
          hasPrevPage: parseInt(page) > 1
        }
      }
    });
  } catch (error) {
    console.error('Favorites fetch error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error fetching favorites'
    });
  }
});

// @route   POST /api/favorites/:productId
// @desc    Add product to favorites
// @access  Private
router.post('/:productId', authenticateToken, async (req, res) => {
  try {
    const { productId } = req.params;

    // Check if product exists and is active
    const product = await Product.findOne({ _id: productId, isActive: true });
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if already in favorites
    const existingFavorite = await Favorite.findOne({
      user: req.user._id,
      product: productId
    });

    if (existingFavorite) {
      return res.status(400).json({
        success: false,
        message: 'Product is already in favorites'
      });
    }

    // Create new favorite
    const favorite = new Favorite({
      user: req.user._id,
      product: productId
    });

    await favorite.save();
    await favorite.populate('product', 'name price originalPrice images category brand rating stock discount');

    res.status(201).json({
      success: true,
      message: 'Product added to favorites successfully',
      data: { favorite }
    });
  } catch (error) {
    console.error('Add to favorites error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error adding product to favorites'
    });
  }
});

// @route   DELETE /api/favorites/:productId
// @desc    Remove product from favorites
// @access  Private
router.delete('/:productId', authenticateToken, async (req, res) => {
  try {
    const { productId } = req.params;

    const favorite = await Favorite.findOneAndDelete({
      user: req.user._id,
      product: productId
    });

    if (!favorite) {
      return res.status(404).json({
        success: false,
        message: 'Product not found in favorites'
      });
    }

    res.json({
      success: true,
      message: 'Product removed from favorites successfully'
    });
  } catch (error) {
    console.error('Remove from favorites error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error removing product from favorites'
    });
  }
});

// @route   GET /api/favorites/check/:productId
// @desc    Check if product is in user's favorites
// @access  Private
router.get('/check/:productId', authenticateToken, async (req, res) => {
  try {
    const { productId } = req.params;

    const favorite = await Favorite.findOne({
      user: req.user._id,
      product: productId
    });

    res.json({
      success: true,
      data: {
        isFavorite: !!favorite
      }
    });
  } catch (error) {
    console.error('Check favorite error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error checking favorite status'
    });
  }
});

// @route   DELETE /api/favorites/clear
// @desc    Clear all favorites
// @access  Private
router.delete('/clear', authenticateToken, async (req, res) => {
  try {
    await Favorite.deleteMany({ user: req.user._id });

    res.json({
      success: true,
      message: 'All favorites cleared successfully'
    });
  } catch (error) {
    console.error('Clear favorites error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error clearing favorites'
    });
  }
});

// @route   GET /api/favorites/count
// @desc    Get count of user's favorites
// @access  Private
router.get('/count', authenticateToken, async (req, res) => {
  try {
    const count = await Favorite.countDocuments({ 
      user: req.user._id,
      product: { $in: await Product.find({ isActive: true }).distinct('_id') }
    });

    res.json({
      success: true,
      data: { count }
    });
  } catch (error) {
    console.error('Favorites count error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error getting favorites count'
    });
  }
});

module.exports = router;
