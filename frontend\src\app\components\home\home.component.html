<div class="container">
  <!-- Hero Section -->
  <section class="hero">
    <div class="hero-content">
      <h1>Welcome to EcomStore</h1>
      <p>Discover amazing products at unbeatable prices</p>
      <div class="hero-actions">
        <a routerLink="/products" class="btn btn-primary">Shop Now</a>
        <a routerLink="/categories" class="btn btn-secondary">Browse Categories</a>
      </div>
    </div>
  </section>

  <!-- Featured Products -->
  <section class="featured-section">
    <h2>Featured Products</h2>
    <div class="products-grid" *ngIf="featuredProducts.length > 0">
      <div class="product-card" *ngFor="let product of featuredProducts">
        <div class="product-image">
          <img [src]="product.images[0]?.url || '/assets/placeholder.jpg'" [alt]="product.name">
          <div class="product-badge" *ngIf="product.isOnSale">Sale</div>
        </div>
        <div class="product-info">
          <h3>{{product.name}}</h3>
          <div class="product-price">
            <span class="current-price">${{product.discountedPrice || product.price}}</span>
            <span class="original-price" *ngIf="product.originalPrice">${{product.originalPrice}}</span>
          </div>
          <div class="product-rating">
            <span class="stars">★★★★☆</span>
            <span class="rating-count">({{product.rating.count}})</span>
          </div>
          <button class="btn btn-primary" (click)="addToCart(product)">Add to Cart</button>
        </div>
      </div>
    </div>
    <div class="loading" *ngIf="loading">
      <p>Loading featured products...</p>
    </div>
  </section>

  <!-- Categories -->
  <section class="categories-section">
    <h2>Shop by Category</h2>
    <div class="categories-grid">
      <div class="category-card" *ngFor="let category of categories">
        <a [routerLink]="['/products']" [queryParams]="{category: category}">
          <div class="category-icon">📱</div>
          <h3>{{category}}</h3>
        </a>
      </div>
    </div>
  </section>
</div>
