{"version": 3, "names": ["_t", "require", "addComment", "PURE_ANNOTATION", "isPureAnnotated", "leadingComments", "some", "comment", "test", "value", "annotateAsPure", "pathOrNode", "node"], "sources": ["../src/index.ts"], "sourcesContent": ["import { addComment, type Node } from \"@babel/types\";\n\nconst PURE_ANNOTATION = \"#__PURE__\";\n\nconst isPureAnnotated = ({ leadingComments }: Node): boolean =>\n  !!leadingComments &&\n  leadingComments.some(comment => /[@#]__PURE__/.test(comment.value));\n\nexport default function annotateAsPure(\n  pathOrNode: Node | { node: Node },\n): void {\n  const node =\n    // @ts-expect-error Node will not have `node` property\n    (pathOrNode.node || pathOrNode) as Node;\n  if (isPureAnnotated(node)) {\n    return;\n  }\n  addComment(node, \"leading\", PURE_ANNOTATION);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,EAAA,GAAAC,OAAA;AAAqD;EAA5CC;AAAU,IAAAF,EAAA;AAEnB,MAAMG,eAAe,GAAG,WAAW;AAEnC,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAsB,CAAC,KAChD,CAAC,CAACA,eAAe,IACjBA,eAAe,CAACC,IAAI,CAACC,OAAO,IAAI,cAAc,CAACC,IAAI,CAACD,OAAO,CAACE,KAAK,CAAC,CAAC;AAEtD,SAASC,cAAcA,CACpCC,UAAiC,EAC3B;EACN,MAAMC,IAAI,GAEPD,UAAU,CAACC,IAAI,IAAID,UAAmB;EACzC,IAAIP,eAAe,CAACQ,IAAI,CAAC,EAAE;IACzB;EACF;EACAV,UAAU,CAACU,IAAI,EAAE,SAAS,EAAET,eAAe,CAAC;AAC9C", "ignoreList": []}