{"name": "p-limit", "version": "4.0.0", "description": "Run multiple promise-returning & async functions with limited concurrency", "license": "MIT", "repository": "sindresorhus/p-limit", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "dependencies": {"yocto-queue": "^1.0.0"}, "devDependencies": {"ava": "^3.15.0", "delay": "^5.0.0", "in-range": "^3.0.0", "random-int": "^3.0.0", "time-span": "^5.0.0", "tsd": "^0.17.0", "xo": "^0.44.0"}}