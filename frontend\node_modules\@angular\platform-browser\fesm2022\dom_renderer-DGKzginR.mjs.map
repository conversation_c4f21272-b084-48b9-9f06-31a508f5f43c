{"version": 3, "file": "dom_renderer-DGKzginR.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-browser/src/dom/events/event_manager.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-browser/src/dom/shared_styles_host.ts", "../../../../../darwin_arm64-fastbuild-ST-2d99d9656325/bin/packages/platform-browser/src/dom/dom_renderer.js"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Inject,\n  Injectable,\n  InjectionToken,\n  NgZone,\n  ɵRuntimeError as RuntimeError,\n  type ListenerOptions,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nexport const EVENT_MANAGER_PLUGINS = new InjectionToken<EventManagerPlugin[]>(\n  ngDevMode ? 'EventManagerPlugins' : '',\n);\n\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\n@Injectable()\nexport class EventManager {\n  private _plugins: EventManagerPlugin[];\n  private _eventNameToPlugin = new Map<string, EventManagerPlugin>();\n\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(\n    @Inject(EVENT_MANAGER_PLUGINS) plugins: EventManagerPlugin[],\n    private _zone: Ng<PERSON>one,\n  ) {\n    plugins.forEach((plugin) => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @param options Options that configure how the event listener is bound.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(\n    element: HTMLElement,\n    eventName: string,\n    handler: Function,\n    options?: ListenerOptions,\n  ): Function {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler, options);\n  }\n\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone(): NgZone {\n    return this._zone;\n  }\n\n  /** @internal */\n  _findPluginFor(eventName: string): EventManagerPlugin {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n\n    const plugins = this._plugins;\n    plugin = plugins.find((plugin) => plugin.supports(eventName));\n    if (!plugin) {\n      throw new RuntimeError(\n        RuntimeErrorCode.NO_PLUGIN_FOR_EVENT,\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n          `No event manager plugin found for event ${eventName}`,\n      );\n    }\n\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n}\n\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nexport abstract class EventManagerPlugin {\n  // TODO: remove (has some usage in G3)\n  constructor(private _doc: any) {}\n\n  // Using non-null assertion because it's set by EventManager's constructor\n  manager!: EventManager;\n\n  /**\n   * Should return `true` for every event name that should be supported by this plugin\n   */\n  abstract supports(eventName: string): boolean;\n\n  /**\n   * Implement the behaviour for the supported events\n   */\n  abstract addEventListener(\n    element: HTMLElement,\n    eventName: string,\n    handler: Function,\n    options?: ListenerOptions,\n  ): Function;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, isPlatformServer} from '@angular/common';\nimport {\n  APP_ID,\n  CSP_NONCE,\n  Inject,\n  Injectable,\n  OnDestroy,\n  Optional,\n  PLATFORM_ID,\n} from '@angular/core';\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n\n/**\n * A record of usage for a specific style including all elements added to the DOM\n * that contain a given style.\n */\ninterface UsageRecord<T> {\n  elements: T[];\n  usage: number;\n}\n\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements: Iterable<HTMLElement>): void {\n  for (const element of elements) {\n    element.remove();\n  }\n}\n\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style: string, doc: Document): HTMLStyleElement {\n  const styleElement = doc.createElement('style');\n  styleElement.textContent = style;\n\n  return styleElement;\n}\n\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(\n  doc: Document,\n  appId: string,\n  inline: Map<string, UsageRecord<HTMLStyleElement>>,\n  external: Map<string, UsageRecord<HTMLLinkElement>>,\n): void {\n  const elements = doc.head?.querySelectorAll<HTMLStyleElement | HTMLLinkElement>(\n    `style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`,\n  );\n\n  if (elements) {\n    for (const styleElement of elements) {\n      styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (styleElement instanceof HTMLLinkElement) {\n        // Only use filename from href\n        // The href is build time generated with a unique value to prevent duplicates.\n        external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n          usage: 0,\n          elements: [styleElement],\n        });\n      } else if (styleElement.textContent) {\n        inline.set(styleElement.textContent, {usage: 0, elements: [styleElement]});\n      }\n    }\n  }\n}\n\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nexport function createLinkElement(url: string, doc: Document): HTMLLinkElement {\n  const linkElement = doc.createElement('link');\n  linkElement.setAttribute('rel', 'stylesheet');\n  linkElement.setAttribute('href', url);\n\n  return linkElement;\n}\n\n@Injectable()\nexport class SharedStylesHost implements OnDestroy {\n  /**\n   * Provides usage information for active inline style content and associated HTML <style> elements.\n   * Embedded styles typically originate from the `styles` metadata of a rendered component.\n   */\n  private readonly inline = new Map<string /** content */, UsageRecord<HTMLStyleElement>>();\n\n  /**\n   * Provides usage information for active external style URLs and the associated HTML <link> elements.\n   * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n   */\n  private readonly external = new Map<string /** URL */, UsageRecord<HTMLLinkElement>>();\n\n  /**\n   * Set of host DOM nodes that will have styles attached.\n   */\n  private readonly hosts = new Set<Node>();\n\n  /**\n   * Whether the application code is currently executing on a server.\n   */\n  private readonly isServer: boolean;\n\n  constructor(\n    @Inject(DOCUMENT) private readonly doc: Document,\n    @Inject(APP_ID) private readonly appId: string,\n    @Inject(CSP_NONCE) @Optional() private readonly nonce?: string | null,\n    @Inject(PLATFORM_ID) platformId: object = {},\n  ) {\n    this.isServer = isPlatformServer(platformId);\n    addServerStyles(doc, appId, this.inline, this.external);\n    this.hosts.add(doc.head);\n  }\n\n  /**\n   * Adds embedded styles to the DOM via HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  addStyles(styles: string[], urls?: string[]): void {\n    for (const value of styles) {\n      this.addUsage(value, this.inline, createStyleElement);\n    }\n\n    urls?.forEach((value) => this.addUsage(value, this.external, createLinkElement));\n  }\n\n  /**\n   * Removes embedded styles from the DOM that were added as HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  removeStyles(styles: string[], urls?: string[]): void {\n    for (const value of styles) {\n      this.removeUsage(value, this.inline);\n    }\n\n    urls?.forEach((value) => this.removeUsage(value, this.external));\n  }\n\n  protected addUsage<T extends HTMLElement>(\n    value: string,\n    usages: Map<string, UsageRecord<T>>,\n    creator: (value: string, doc: Document) => T,\n  ): void {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n\n    // If existing, just increment the usage count\n    if (record) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n        // A usage count of zero indicates a preexisting server generated style.\n        // This attribute is solely used for debugging purposes of SSR style reuse.\n        record.elements.forEach((element) => element.setAttribute('ng-style-reused', ''));\n      }\n      record.usage++;\n    } else {\n      // Otherwise, create an entry to track the elements and add element for each host\n      usages.set(value, {\n        usage: 1,\n        elements: [...this.hosts].map((host) => this.addElement(host, creator(value, this.doc))),\n      });\n    }\n  }\n\n  protected removeUsage<T extends HTMLElement>(\n    value: string,\n    usages: Map<string, UsageRecord<T>>,\n  ): void {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n\n    // If there is a record, reduce the usage count and if no longer used,\n    // remove from DOM and delete usage record.\n    if (record) {\n      record.usage--;\n      if (record.usage <= 0) {\n        removeElements(record.elements);\n        usages.delete(value);\n      }\n    }\n  }\n\n  ngOnDestroy(): void {\n    for (const [, {elements}] of [...this.inline, ...this.external]) {\n      removeElements(elements);\n    }\n    this.hosts.clear();\n  }\n\n  /**\n   * Adds a host node to the set of style hosts and adds all existing style usage to\n   * the newly added host node.\n   *\n   * This is currently only used for Shadow DOM encapsulation mode.\n   */\n  addHost(hostNode: Node): void {\n    this.hosts.add(hostNode);\n\n    // Add existing styles to new host\n    for (const [style, {elements}] of this.inline) {\n      elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n    }\n    for (const [url, {elements}] of this.external) {\n      elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n    }\n  }\n\n  removeHost(hostNode: Node): void {\n    this.hosts.delete(hostNode);\n  }\n\n  private addElement<T extends HTMLElement>(host: Node, element: T): T {\n    // Add a nonce if present\n    if (this.nonce) {\n      element.setAttribute('nonce', this.nonce);\n    }\n\n    // Add application identifier when on the server to support client-side reuse\n    if (this.isServer) {\n      element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n    }\n\n    // Insert the element into the DOM with the host node as parent\n    return host.appendChild(element);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport { DOCUMENT, isPlatformServer, ɵgetDOM as getDOM } from '@angular/common';\nimport { APP_ID, CSP_NONCE, Inject, Injectable, InjectionToken, NgZone, PLATFORM_ID, RendererStyleFlags2, ViewEncapsulation, ɵRuntimeError as RuntimeError, ɵTracingService as TracingService, Optional, } from '@angular/core';\nimport { EventManager } from './events/event_manager';\nimport { createLinkElement, SharedStylesHost } from './shared_styles_host';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./events/event_manager\";\nimport * as i2 from \"./shared_styles_host\";\nexport const NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/Math/MathML',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\nexport const COMPONENT_VARIABLE = '%COMP%';\nexport const HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nexport const CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nexport const REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nexport function shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nexport function shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nexport function shimStylesContent(compId, styles) {\n    return styles.map((s) => s.replace(COMPONENT_REGEX, compId));\n}\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nexport function addBaseHrefToCssSourceMap(baseHref, styles) {\n    if (!baseHref) {\n        return styles;\n    }\n    const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n    return styles.map((cssContent) => {\n        if (!cssContent.includes('sourceMappingURL=')) {\n            return cssContent;\n        }\n        return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n            if (sourceMapUrl[0] === '/' ||\n                sourceMapUrl.startsWith('data:') ||\n                PROTOCOL_REGEXP.test(sourceMapUrl)) {\n                return `/*# sourceMappingURL=${sourceMapUrl} */`;\n            }\n            const { pathname: resolvedSourceMapUrl } = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n            return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n        });\n    });\n}\nexport class DomRendererFactory2 {\n    eventManager;\n    sharedStylesHost;\n    appId;\n    removeStylesOnCompDestroy;\n    doc;\n    platformId;\n    ngZone;\n    nonce;\n    tracingService;\n    rendererByCompId = new Map();\n    defaultRenderer;\n    platformIsServer;\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null, tracingService = null) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.doc = doc;\n        this.platformId = platformId;\n        this.ngZone = ngZone;\n        this.nonce = nonce;\n        this.tracingService = tracingService;\n        this.platformIsServer = isPlatformServer(platformId);\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer, this.tracingService);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n            // Domino does not support shadow DOM.\n            type = { ...type, encapsulation: ViewEncapsulation.Emulated };\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const doc = this.doc;\n            const ngZone = this.ngZone;\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n            const platformIsServer = this.platformIsServer;\n            const tracingService = this.tracingService;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer, tracingService);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService);\n                    break;\n            }\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    /**\n     * Used during HMR to clear any cached data about a component.\n     * @param componentId ID of the component that is being replaced.\n     */\n    componentReplaced(componentId) {\n        this.rendererByCompId.delete(componentId);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: i1.EventManager }, { token: i2.SharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: CSP_NONCE }, { token: TracingService, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DomRendererFactory2 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i1.EventManager }, { type: i2.SharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }, { type: i0.ɵTracingService, decorators: [{\n                    type: Inject,\n                    args: [TracingService]\n                }, {\n                    type: Optional\n                }] }] });\nclass DefaultDomRenderer2 {\n    eventManager;\n    doc;\n    ngZone;\n    platformIsServer;\n    tracingService;\n    data = Object.create(null);\n    /**\n     * By default this renderer throws when encountering synthetic properties\n     * This can be disabled for example by the AsyncAnimationRendererFactory\n     */\n    throwOnSyntheticProps = true;\n    constructor(eventManager, doc, ngZone, platformIsServer, tracingService) {\n        this.eventManager = eventManager;\n        this.doc = doc;\n        this.ngZone = ngZone;\n        this.platformIsServer = platformIsServer;\n        this.tracingService = tracingService;\n    }\n    destroy() { }\n    destroyNode = null;\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return this.doc.createElement(name);\n    }\n    createComment(value) {\n        return this.doc.createComment(value);\n    }\n    createText(value) {\n        return this.doc.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(_parent, oldChild) {\n        oldChild.remove();\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n        if (!el) {\n            throw new RuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        if (el == null) {\n            return;\n        }\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback, options) {\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            target = getDOM().getGlobalEventTarget(this.doc, target);\n            if (!target) {\n                throw new RuntimeError(5102 /* RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `Unsupported event target ${target} for event ${event}`);\n            }\n        }\n        let wrappedCallback = this.decoratePreventDefault(callback);\n        if (this.tracingService?.wrapEventListener) {\n            wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n        }\n        return this.eventManager.addEventListener(target, event, wrappedCallback, options);\n    }\n    decoratePreventDefault(eventHandler) {\n        // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n        // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n        // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n        // unwrap the listener (see below).\n        return (event) => {\n            // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n            // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n            // debug_node can inspect the listener toString contents for the existence of this special\n            // token. Because the token is a string literal, it is ensured to not be modified by compiled\n            // code.\n            if (event === '__ngUnwrap__') {\n                return eventHandler;\n            }\n            // Run the event handler inside the ngZone because event handlers are not patched\n            // by Zone on the server. This is required only for tests.\n            const allowDefaultBehavior = this.platformIsServer\n                ? this.ngZone.runGuarded(() => eventHandler(event))\n                : eventHandler(event);\n            if (allowDefaultBehavior === false) {\n                event.preventDefault();\n            }\n            return undefined;\n        };\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new RuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    sharedStylesHost;\n    hostEl;\n    shadowRoot;\n    constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer, tracingService) {\n        super(eventManager, doc, ngZone, platformIsServer, tracingService);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        let styles = component.styles;\n        if (ngDevMode) {\n            // We only do this in development, as for production users should not add CSS sourcemaps to components.\n            const baseHref = getDOM().getBaseHref(doc) ?? '';\n            styles = addBaseHrefToCssSourceMap(baseHref, styles);\n        }\n        styles = shimStylesContent(component.id, styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            if (nonce) {\n                styleEl.setAttribute('nonce', nonce);\n            }\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n        // Apply any external component styles to the shadow root for the component's element.\n        // The ShadowDOM renderer uses an alternative execution path for component styles that\n        // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n        // the manual addition of embedded styles directly above, any external stylesheets\n        // must be manually added here to ensure ShadowDOM components are correctly styled.\n        // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n        const styleUrls = component.getExternalStyles?.();\n        if (styleUrls) {\n            for (const styleUrl of styleUrls) {\n                const linkEl = createLinkElement(styleUrl, doc);\n                if (nonce) {\n                    linkEl.setAttribute('nonce', nonce);\n                }\n                this.shadowRoot.appendChild(linkEl);\n            }\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(_parent, oldChild) {\n        return super.removeChild(null, oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    sharedStylesHost;\n    removeStylesOnCompDestroy;\n    styles;\n    styleUrls;\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId) {\n        super(eventManager, doc, ngZone, platformIsServer, tracingService);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        let styles = component.styles;\n        if (ngDevMode) {\n            // We only do this in development, as for production users should not add CSS sourcemaps to components.\n            const baseHref = getDOM().getBaseHref(doc) ?? '';\n            styles = addBaseHrefToCssSourceMap(baseHref, styles);\n        }\n        this.styles = compId ? shimStylesContent(compId, styles) : styles;\n        this.styleUrls = component.getExternalStyles?.(compId);\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestroy) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    contentAttr;\n    hostAttr;\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, tracingService, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n//# sourceMappingURL=dom_renderer.js.map"], "names": ["RuntimeError", "i1.EventManager", "i2.SharedStylesHost", "TracingService", "getDOM"], "mappings": ";;;;;;;;;;AAmBA;;;;AAIG;AACU,MAAA,qBAAqB,GAAG,IAAI,cAAc,CACrD,SAAS,GAAG,qBAAqB,GAAG,EAAE;AAGxC;;;;;AAKG;MAEU,YAAY,CAAA;AASb,IAAA,KAAA;AARF,IAAA,QAAQ;AACR,IAAA,kBAAkB,GAAG,IAAI,GAAG,EAA8B;AAElE;;AAEG;IACH,WACiC,CAAA,OAA6B,EACpD,KAAa,EAAA;QAAb,IAAK,CAAA,KAAA,GAAL,KAAK;AAEb,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACzB,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI;AACvB,SAAC,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE;;AAG3C;;;;;;;;;AASG;AACH,IAAA,gBAAgB,CACd,OAAoB,EACpB,SAAiB,EACjB,OAAiB,EACjB,OAAyB,EAAA;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AAC7C,QAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;;AAGtE;;AAEG;IACH,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,KAAK;;;AAInB,IAAA,cAAc,CAAC,SAAiB,EAAA;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC;QACnD,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,MAAM;;AAGf,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;AAC7B,QAAA,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC7D,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAIA,aAAY,CAAA,IAAA,6CAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC5C,CAA2C,wCAAA,EAAA,SAAS,CAAE,CAAA,CACzD;;QAGH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;AAC9C,QAAA,OAAO,MAAM;;AA9DJ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBAQb,qBAAqB,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHARpB,YAAY,EAAA,CAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB;;0BASI,MAAM;2BAAC,qBAAqB;;AA0DjC;;;;;;;AAOG;MACmB,kBAAkB,CAAA;AAElB,IAAA,IAAA;;AAApB,IAAA,WAAA,CAAoB,IAAS,EAAA;QAAT,IAAI,CAAA,IAAA,GAAJ,IAAI;;;AAGxB,IAAA,OAAO;AAgBR;;AC/GD;AACA,MAAM,qBAAqB,GAAG,WAAW;AAWzC;;;AAGG;AACH,SAAS,cAAc,CAAC,QAA+B,EAAA;AACrD,IAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,OAAO,CAAC,MAAM,EAAE;;AAEpB;AAEA;;;;;AAKG;AACH,SAAS,kBAAkB,CAAC,KAAa,EAAE,GAAa,EAAA;IACtD,MAAM,YAAY,GAAG,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC;AAC/C,IAAA,YAAY,CAAC,WAAW,GAAG,KAAK;AAEhC,IAAA,OAAO,YAAY;AACrB;AAEA;;;;;;;AAOG;AACH,SAAS,eAAe,CACtB,GAAa,EACb,KAAa,EACb,MAAkD,EAClD,QAAmD,EAAA;AAEnD,IAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,EAAE,gBAAgB,CACzC,CAAA,MAAA,EAAS,qBAAqB,CAAA,EAAA,EAAK,KAAK,CAAW,QAAA,EAAA,qBAAqB,KAAK,KAAK,CAAA,EAAA,CAAI,CACvF;IAED,IAAI,QAAQ,EAAE;AACZ,QAAA,KAAK,MAAM,YAAY,IAAI,QAAQ,EAAE;AACnC,YAAA,YAAY,CAAC,eAAe,CAAC,qBAAqB,CAAC;AACnD,YAAA,IAAI,YAAY,YAAY,eAAe,EAAE;;;gBAG3C,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAC5E,oBAAA,KAAK,EAAE,CAAC;oBACR,QAAQ,EAAE,CAAC,YAAY,CAAC;AACzB,iBAAA,CAAC;;AACG,iBAAA,IAAI,YAAY,CAAC,WAAW,EAAE;AACnC,gBAAA,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,EAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,YAAY,CAAC,EAAC,CAAC;;;;AAIlF;AAEA;;;;;AAKG;AACa,SAAA,iBAAiB,CAAC,GAAW,EAAE,GAAa,EAAA;IAC1D,MAAM,WAAW,GAAG,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC;AAC7C,IAAA,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC;AAC7C,IAAA,WAAW,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;AAErC,IAAA,OAAO,WAAW;AACpB;MAGa,gBAAgB,CAAA;AAwBU,IAAA,GAAA;AACF,IAAA,KAAA;AACe,IAAA,KAAA;AAzBlD;;;AAGG;AACc,IAAA,MAAM,GAAG,IAAI,GAAG,EAAwD;AAEzF;;;AAGG;AACc,IAAA,QAAQ,GAAG,IAAI,GAAG,EAAmD;AAEtF;;AAEG;AACc,IAAA,KAAK,GAAG,IAAI,GAAG,EAAQ;AAExC;;AAEG;AACc,IAAA,QAAQ;AAEzB,IAAA,WAAA,CACqC,GAAa,EACf,KAAa,EACE,KAAqB,EAChD,aAAqB,EAAE,EAAA;QAHT,IAAG,CAAA,GAAA,GAAH,GAAG;QACL,IAAK,CAAA,KAAA,GAAL,KAAK;QACU,IAAK,CAAA,KAAA,GAAL,KAAK;AAGrD,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,UAAU,CAAC;AAC5C,QAAA,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC;QACvD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;;AAG1B;;;AAGG;IACH,SAAS,CAAC,MAAgB,EAAE,IAAe,EAAA;AACzC,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;;QAGvD,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;;AAGlF;;;AAGG;IACH,YAAY,CAAC,MAAgB,EAAE,IAAe,EAAA;AAC5C,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC;;AAGtC,QAAA,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;AAGxD,IAAA,QAAQ,CAChB,KAAa,EACb,MAAmC,EACnC,OAA4C,EAAA;;QAG5C,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;;QAGhC,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;;;AAGzE,gBAAA,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;;YAEnF,MAAM,CAAC,KAAK,EAAE;;aACT;;AAEL,YAAA,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE;AAChB,gBAAA,KAAK,EAAE,CAAC;AACR,gBAAA,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACzF,aAAA,CAAC;;;IAII,WAAW,CACnB,KAAa,EACb,MAAmC,EAAA;;QAGnC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;;;QAIhC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE;AACrB,gBAAA,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC/B,gBAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;;;;IAK1B,WAAW,GAAA;QACT,KAAK,MAAM,GAAG,EAAC,QAAQ,EAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC/D,cAAc,CAAC,QAAQ,CAAC;;AAE1B,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;;AAGpB;;;;;AAKG;AACH,IAAA,OAAO,CAAC,QAAc,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;;AAGxB,QAAA,KAAK,MAAM,CAAC,KAAK,EAAE,EAAC,QAAQ,EAAC,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AAC7C,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;AAE/E,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,EAAC,QAAQ,EAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC7C,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;AAI9E,IAAA,UAAU,CAAC,QAAc,EAAA;AACvB,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;;IAGrB,UAAU,CAAwB,IAAU,EAAE,OAAU,EAAA;;AAE9D,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC;;;AAI3C,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC;;;AAIzD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;;AA9IvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,kBAwBjB,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,MAAM,EACN,EAAA,EAAA,KAAA,EAAA,SAAS,6BACT,WAAW,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHA3BV,gBAAgB,EAAA,CAAA;;sGAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B;;0BAyBI,MAAM;2BAAC,QAAQ;;0BACf,MAAM;2BAAC,MAAM;;0BACb,MAAM;2BAAC,SAAS;;0BAAG;;0BACnB,MAAM;2BAAC,WAAW;;;ACrHhB,MAAM,cAAc,GAAG;AAC9B,IAAI,KAAK,EAAE,4BAA4B;AACvC,IAAI,OAAO,EAAE,8BAA8B;AAC3C,IAAI,OAAO,EAAE,8BAA8B;AAC3C,IAAI,KAAK,EAAE,sCAAsC;AACjD,IAAI,OAAO,EAAE,+BAA+B;AAC5C,IAAI,MAAM,EAAE,oCAAoC;AAChD,CAAC;AACD,MAAM,eAAe,GAAG,SAAS;AACjC,MAAM,oBAAoB,GAAG,uCAAuC;AACpE,MAAM,eAAe,GAAG,UAAU;AAC3B,MAAM,kBAAkB,GAAG,QAAQ;AACnC,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;AACjD,MAAM,YAAY,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;AAC9D;AACA;AACA;AACA,MAAM,0CAA0C,GAAG,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACY,MAAC,kCAAkC,GAAG,IAAI,cAAc,CAAC,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAAE;AACnH,IAAI,UAAU,EAAE,MAAM;AACtB,IAAI,OAAO,EAAE,MAAM,0CAA0C;AAC7D,CAAC;AACM,SAAS,oBAAoB,CAAC,gBAAgB,EAAE;AACvD,IAAI,OAAO,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,gBAAgB,CAAC;AAClE;AACO,SAAS,iBAAiB,CAAC,gBAAgB,EAAE;AACpD,IAAI,OAAO,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,gBAAgB,CAAC;AAC/D;AACO,SAAS,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE;AAClD,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACO,SAAS,yBAAyB,CAAC,QAAQ,EAAE,MAAM,EAAE;AAC5D,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,QAAQ,OAAO,MAAM;AACrB;AACA,IAAI,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC;AACrE,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,KAAK;AACtC,QAAQ,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;AACvD,YAAY,OAAO,UAAU;AAC7B;AACA,QAAQ,OAAO,UAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE,YAAY,KAAK;AAC7E,YAAY,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG;AACvC,gBAAgB,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC;AAChD,gBAAgB,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;AACpD,gBAAgB,OAAO,CAAC,qBAAqB,EAAE,YAAY,CAAC,GAAG,CAAC;AAChE;AACA,YAAY,MAAM,EAAE,QAAQ,EAAE,oBAAoB,EAAE,GAAG,IAAI,GAAG,CAAC,YAAY,EAAE,mBAAmB,CAAC;AACjG,YAAY,OAAO,CAAC,qBAAqB,EAAE,oBAAoB,CAAC,GAAG,CAAC;AACpE,SAAS,CAAC;AACV,KAAK,CAAC;AACN;AACO,MAAM,mBAAmB,CAAC;AACjC,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,KAAK;AACT,IAAI,yBAAyB;AAC7B,IAAI,GAAG;AACP,IAAI,UAAU;AACd,IAAI,MAAM;AACV,IAAI,KAAK;AACT,IAAI,cAAc;AAClB,IAAI,gBAAgB,GAAG,IAAI,GAAG,EAAE;AAChC,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,WAAW,CAAC,YAAY,EAAE,gBAAgB,EAAE,KAAK,EAAE,yBAAyB,EAAE,GAAG,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,GAAG,IAAI,EAAE,cAAc,GAAG,IAAI,EAAE;AAChJ,QAAQ,IAAI,CAAC,YAAY,GAAG,YAAY;AACxC,QAAQ,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;AAChD,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK;AAC1B,QAAQ,IAAI,CAAC,yBAAyB,GAAG,yBAAyB;AAClE,QAAQ,IAAI,CAAC,GAAG,GAAG,GAAG;AACtB,QAAQ,IAAI,CAAC,UAAU,GAAG,UAAU;AACpC,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM;AAC5B,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK;AAC1B,QAAQ,IAAI,CAAC,cAAc,GAAG,cAAc;AAC5C,QAAQ,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,UAAU,CAAC;AAC5D,QAAQ,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,cAAc,CAAC;AAC7H;AACA,IAAI,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE;AAClC,QAAQ,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE;AAC/B,YAAY,OAAO,IAAI,CAAC,eAAe;AACvC;AACA,QAAQ,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,KAAK,iBAAiB,CAAC,SAAS,EAAE;AACzF;AACA,YAAY,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa,EAAE,iBAAiB,CAAC,QAAQ,EAAE;AACzE;AACA,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC;AAChE;AACA;AACA,QAAQ,IAAI,QAAQ,YAAY,iCAAiC,EAAE;AACnE,YAAY,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC;AACzC;AACA,aAAa,IAAI,QAAQ,YAAY,4BAA4B,EAAE;AACnE,YAAY,QAAQ,CAAC,WAAW,EAAE;AAClC;AACA,QAAQ,OAAO,QAAQ;AACvB;AACA,IAAI,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE;AACvC,QAAQ,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;AACtD,QAAQ,IAAI,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;AACpD,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACvB,YAAY,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;AAChC,YAAY,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AACtC,YAAY,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;AAClD,YAAY,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;AAC1D,YAAY,MAAM,yBAAyB,GAAG,IAAI,CAAC,yBAAyB;AAC5E,YAAY,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;AAC1D,YAAY,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc;AACtD,YAAY,QAAQ,IAAI,CAAC,aAAa;AACtC,gBAAgB,KAAK,iBAAiB,CAAC,QAAQ;AAC/C,oBAAoB,QAAQ,GAAG,IAAI,iCAAiC,CAAC,YAAY,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,yBAAyB,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,CAAC;AAChM,oBAAoB;AACpB,gBAAgB,KAAK,iBAAiB,CAAC,SAAS;AAChD,oBAAoB,OAAO,IAAI,iBAAiB,CAAC,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,gBAAgB,EAAE,cAAc,CAAC;AAC1J,gBAAgB;AAChB,oBAAoB,QAAQ,GAAG,IAAI,4BAA4B,CAAC,YAAY,EAAE,gBAAgB,EAAE,IAAI,EAAE,yBAAyB,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,CAAC;AAC/K,oBAAoB;AACpB;AACA,YAAY,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;AACnD;AACA,QAAQ,OAAO,QAAQ;AACvB;AACA,IAAI,WAAW,GAAG;AAClB,QAAQ,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AACrC;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,CAAC,WAAW,EAAE;AACnC,QAAQ,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC;AACjD;AACA,IAAI,OAAO,IAAI,GAAG,EAAE,CAAC,kBAAkB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAEC,YAAe,EAAE,EAAE,EAAE,KAAK,EAAEC,gBAAmB,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,kCAAkC,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAEC,eAAc,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;AACzb,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC,qBAAqB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,CAAC;AAC5I;AACA,EAAE,CAAC,wBAAwB,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,mBAAmB,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,mBAAmB,EAAE,UAAU,EAAE,CAAC;AACxI,YAAY,IAAI,EAAE;AAClB,SAAS,CAAC,EAAE,cAAc,EAAE,MAAM,CAAC,EAAE,IAAI,EAAEF,YAAe,EAAE,EAAE,EAAE,IAAI,EAAEC,gBAAmB,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AAC7H,oBAAoB,IAAI,EAAE,MAAM;AAChC,oBAAoB,IAAI,EAAE,CAAC,MAAM;AACjC,iBAAiB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AACtD,oBAAoB,IAAI,EAAE,MAAM;AAChC,oBAAoB,IAAI,EAAE,CAAC,kCAAkC;AAC7D,iBAAiB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC;AACrD,oBAAoB,IAAI,EAAE,MAAM;AAChC,oBAAoB,IAAI,EAAE,CAAC,QAAQ;AACnC,iBAAiB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AACnD,oBAAoB,IAAI,EAAE,MAAM;AAChC,oBAAoB,IAAI,EAAE,CAAC,WAAW;AACtC,iBAAiB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;AAC3E,oBAAoB,IAAI,EAAE,MAAM;AAChC,oBAAoB,IAAI,EAAE,CAAC,SAAS;AACpC,iBAAiB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,CAAC,eAAe,EAAE,UAAU,EAAE,CAAC;AAC/D,oBAAoB,IAAI,EAAE,MAAM;AAChC,oBAAoB,IAAI,EAAE,CAACC,eAAc;AACzC,iBAAiB,EAAE;AACnB,oBAAoB,IAAI,EAAE;AAC1B,iBAAiB,CAAC,EAAE,CAAC,EAAE,CAAC;AACxB,MAAM,mBAAmB,CAAC;AAC1B,IAAI,YAAY;AAChB,IAAI,GAAG;AACP,IAAI,MAAM;AACV,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;AAC9B;AACA;AACA;AACA;AACA,IAAI,qBAAqB,GAAG,IAAI;AAChC,IAAI,WAAW,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE;AAC7E,QAAQ,IAAI,CAAC,YAAY,GAAG,YAAY;AACxC,QAAQ,IAAI,CAAC,GAAG,GAAG,GAAG;AACtB,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM;AAC5B,QAAQ,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;AAChD,QAAQ,IAAI,CAAC,cAAc,GAAG,cAAc;AAC5C;AACA,IAAI,OAAO,GAAG;AACd,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,aAAa,CAAC,IAAI,EAAE,SAAS,EAAE;AACnC,QAAQ,IAAI,SAAS,EAAE;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,SAAS,EAAE,IAAI,CAAC;AACzF;AACA,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC;AAC3C;AACA,IAAI,aAAa,CAAC,KAAK,EAAE;AACzB,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC;AAC5C;AACA,IAAI,UAAU,CAAC,KAAK,EAAE;AACtB,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC;AAC7C;AACA,IAAI,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAClC,QAAQ,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM;AAC7E,QAAQ,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC1C;AACA,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC7C,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM;AACjF,YAAY,YAAY,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACzD;AACA;AACA,IAAI,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,EAAE;AACzB;AACA,IAAI,iBAAiB,CAAC,cAAc,EAAE,eAAe,EAAE;AACvD,QAAQ,IAAI,EAAE,GAAG,OAAO,cAAc,KAAK,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,cAAc;AAC7G,QAAQ,IAAI,CAAC,EAAE,EAAE;AACjB,YAAY,MAAM,IAAIH,aAAY,CAAC,KAAK,6CAA6C,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AACnI,gBAAgB,CAAC,cAAc,EAAE,cAAc,CAAC,4BAA4B,CAAC,CAAC;AAC9E;AACA,QAAQ,IAAI,CAAC,eAAe,EAAE;AAC9B,YAAY,EAAE,CAAC,WAAW,GAAG,EAAE;AAC/B;AACA,QAAQ,OAAO,EAAE;AACjB;AACA,IAAI,UAAU,CAAC,IAAI,EAAE;AACrB,QAAQ,OAAO,IAAI,CAAC,UAAU;AAC9B;AACA,IAAI,WAAW,CAAC,IAAI,EAAE;AACtB,QAAQ,OAAO,IAAI,CAAC,WAAW;AAC/B;AACA,IAAI,YAAY,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;AAC7C,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,IAAI,GAAG,SAAS,GAAG,GAAG,GAAG,IAAI;AACzC,YAAY,MAAM,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC;AAC1D,YAAY,IAAI,YAAY,EAAE;AAC9B,gBAAgB,EAAE,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC;AAC5D;AACA,iBAAiB;AACjB,gBAAgB,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;AAC5C;AACA;AACA,aAAa;AACb,YAAY,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;AACxC;AACA;AACA,IAAI,eAAe,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;AACzC,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,MAAM,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC;AAC1D,YAAY,IAAI,YAAY,EAAE;AAC9B,gBAAgB,EAAE,CAAC,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC;AACxD;AACA,iBAAiB;AACjB,gBAAgB,EAAE,CAAC,eAAe,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1D;AACA;AACA,aAAa;AACb,YAAY,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;AACpC;AACA;AACA,IAAI,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE;AACvB,QAAQ,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;AAC9B;AACA,IAAI,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE;AAC1B,QAAQ,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AACjC;AACA,IAAI,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AACtC,QAAQ,IAAI,KAAK,IAAI,mBAAmB,CAAC,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,EAAE;AACpF,YAAY,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,mBAAmB,CAAC,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;AACxG;AACA,aAAa;AACb,YAAY,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK;AACnC;AACA;AACA,IAAI,WAAW,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;AAClC,QAAQ,IAAI,KAAK,GAAG,mBAAmB,CAAC,QAAQ,EAAE;AAClD;AACA,YAAY,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;AAC1C;AACA,aAAa;AACb,YAAY,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE;AAChC;AACA;AACA,IAAI,WAAW,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AACjC,QAAQ,IAAI,EAAE,IAAI,IAAI,EAAE;AACxB,YAAY;AACZ;AACA,QAAQ,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AACtD,YAAY,IAAI,CAAC,qBAAqB;AACtC,YAAY,oBAAoB,CAAC,IAAI,EAAE,UAAU,CAAC;AAClD,QAAQ,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK;AACxB;AACA,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;AAC1B,QAAQ,IAAI,CAAC,SAAS,GAAG,KAAK;AAC9B;AACA,IAAI,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE;AAC7C,QAAQ,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AACtD,YAAY,IAAI,CAAC,qBAAqB;AACtC,YAAY,oBAAoB,CAAC,KAAK,EAAE,UAAU,CAAC;AACnD,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AACxC,YAAY,MAAM,GAAGI,OAAM,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC;AACpE,YAAY,IAAI,CAAC,MAAM,EAAE;AACzB,gBAAgB,MAAM,IAAIJ,aAAY,CAAC,IAAI,kDAAkD,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC3I,oBAAoB,CAAC,yBAAyB,EAAE,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;AAC5E;AACA;AACA,QAAQ,IAAI,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC;AACnE,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE,iBAAiB,EAAE;AACpD,YAAY,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC;AACnG;AACA,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,OAAO,CAAC;AAC1F;AACA,IAAI,sBAAsB,CAAC,YAAY,EAAE;AACzC;AACA;AACA;AACA;AACA,QAAQ,OAAO,CAAC,KAAK,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA,YAAY,IAAI,KAAK,KAAK,cAAc,EAAE;AAC1C,gBAAgB,OAAO,YAAY;AACnC;AACA;AACA;AACA,YAAY,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAC9C,kBAAkB,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC;AAClE,kBAAkB,YAAY,CAAC,KAAK,CAAC;AACrC,YAAY,IAAI,oBAAoB,KAAK,KAAK,EAAE;AAChD,gBAAgB,KAAK,CAAC,cAAc,EAAE;AACtC;AACA,YAAY,OAAO,SAAS;AAC5B,SAAS;AACT;AACA;AACA,MAAM,WAAW,GAAG,CAAC,MAAM,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG;AAC/C,SAAS,oBAAoB,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC9C,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AAC5C,QAAQ,MAAM,IAAIA,aAAY,CAAC,IAAI,uDAAuD,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC;AACnI;AACA,6DAA6D,EAAE,IAAI,CAAC,+HAA+H,CAAC,CAAC;AACrM;AACA;AACA,SAAS,cAAc,CAAC,IAAI,EAAE;AAC9B,IAAI,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS;AACpE;AACA,MAAM,iBAAiB,SAAS,mBAAmB,CAAC;AACpD,IAAI,gBAAgB;AACpB,IAAI,MAAM;AACV,IAAI,UAAU;AACd,IAAI,WAAW,CAAC,YAAY,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE;AACzH,QAAQ,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,CAAC;AAC1E,QAAQ,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;AAChD,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM;AAC5B,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;AAC/D,QAAQ,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;AACtD,QAAQ,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM;AACrC,QAAQ,IAAI,SAAS,EAAE;AACvB;AACA,YAAY,MAAM,QAAQ,GAAGI,OAAM,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5D,YAAY,MAAM,GAAG,yBAAyB,CAAC,QAAQ,EAAE,MAAM,CAAC;AAChE;AACA,QAAQ,MAAM,GAAG,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC;AACxD,QAAQ,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AACpC,YAAY,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AAC3D,YAAY,IAAI,KAAK,EAAE;AACvB,gBAAgB,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;AACpD;AACA,YAAY,OAAO,CAAC,WAAW,GAAG,KAAK;AACvC,YAAY,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,IAAI;AACzD,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAC9C,gBAAgB,MAAM,MAAM,GAAG,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC;AAC/D,gBAAgB,IAAI,KAAK,EAAE;AAC3B,oBAAoB,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;AACvD;AACA,gBAAgB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC;AACnD;AACA;AACA;AACA,IAAI,gBAAgB,CAAC,IAAI,EAAE;AAC3B,QAAQ,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI;AAC5D;AACA,IAAI,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE;AAClC,QAAQ,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC;AACzE;AACA,IAAI,YAAY,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC7C,QAAQ,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC;AACpF;AACA,IAAI,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE;AACnC,QAAQ,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;AAChD;AACA,IAAI,UAAU,CAAC,IAAI,EAAE;AACrB,QAAQ,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;AACnF;AACA,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;AACzD;AACA;AACA,MAAM,4BAA4B,SAAS,mBAAmB,CAAC;AAC/D,IAAI,gBAAgB;AACpB,IAAI,yBAAyB;AAC7B,IAAI,MAAM;AACV,IAAI,SAAS;AACb,IAAI,WAAW,CAAC,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,yBAAyB,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,MAAM,EAAE;AAC7I,QAAQ,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,CAAC;AAC1E,QAAQ,IAAI,CAAC,gBAAgB,GAAG,gBAAgB;AAChD,QAAQ,IAAI,CAAC,yBAAyB,GAAG,yBAAyB;AAClE,QAAQ,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM;AACrC,QAAQ,IAAI,SAAS,EAAE;AACvB;AACA,YAAY,MAAM,QAAQ,GAAGA,OAAM,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE;AAC5D,YAAY,MAAM,GAAG,yBAAyB,CAAC,QAAQ,EAAE,MAAM,CAAC;AAChE;AACA,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM;AACzE,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,iBAAiB,GAAG,MAAM,CAAC;AAC9D;AACA,IAAI,WAAW,GAAG;AAClB,QAAQ,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC;AACpE;AACA,IAAI,OAAO,GAAG;AACd,QAAQ,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;AAC7C,YAAY;AACZ;AACA,QAAQ,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC;AACvE;AACA;AACA,MAAM,iCAAiC,SAAS,4BAA4B,CAAC;AAC7E,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,WAAW,CAAC,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,KAAK,EAAE,yBAAyB,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE;AAC5I,QAAQ,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,SAAS,CAAC,EAAE;AACjD,QAAQ,KAAK,CAAC,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,yBAAyB,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,EAAE,MAAM,CAAC;AAC1I,QAAQ,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC,MAAM,CAAC;AACvD,QAAQ,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC,MAAM,CAAC;AACjD;AACA,IAAI,WAAW,CAAC,OAAO,EAAE;AACzB,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,QAAQ,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;AACrD;AACA,IAAI,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE;AAChC,QAAQ,MAAM,EAAE,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC;AACpD,QAAQ,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;AACpD,QAAQ,OAAO,EAAE;AACjB;AACA;;;;"}